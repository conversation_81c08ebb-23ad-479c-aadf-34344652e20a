"use client";

import React, { useContext, useState } from "react"; // Added useContext, useState
import Image from "next/image";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { CartContext } from "@/context/CartProvider"; // Changed import for useCart
// Removed: import { ApiRequest, ApiResponse, ApiDishForResponse } from "@/types/request";
import { toast } from "sonner";
import { ShoppingCart } from "lucide-react";
import { acceptResponse } from "@/api/requests";

// --- START TYPE DEFINITIONS (copied from app/requests/[id]/page.tsx for now) ---
interface ApiLocation {
  _id?: string;
  street?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  coordinates?: [number, number];
  postalCode?: string;
  latitude?: number;
  longitude?: number;
}

interface ApiUser {
  _id: string;
  name?: string;
  avatar?: string;
}

interface ApiDishRequest {
  title: string;
  ingredients: string[];
  description?: string;
  offering: string;
  minServing: number;
  maxServing: number;
  quantity?: number;
  offeringTime?: string; // ISO Date string
}

interface ApiDishForResponse {
  _id: string;
  name: string;
  price: number;
  photos?: string[];
  description?: string;
}

interface ApiAcceptedResponseData {
  _id: string;
  host: ApiUser;
  dish: ApiDishForResponse;
  message: string;
  status: string;
}

interface ApiRequest {
  _id: string;
  user: ApiUser;
  dishRequest: ApiDishRequest;
  status:
    | "pending_responses"
    | "confirmed"
    | "closed"
    | "assigned"
    | "active"
    | "open";
  location: ApiLocation;
  city?: string;
  responseCount?: number;
  expiresAt?: string | null;
  createdAt: string;
  updatedAt: string;
  acceptedResponseId?: string | ApiAcceptedResponseData;
  description?: string;
  offering?: string;
  offeringTime?: string;
}

interface ApiResponse {
  _id: string;
  request: string; // Request ID
  host: ApiUser;
  dish?: ApiDishForResponse;
  message: string;
  status: "pending_user_action" | "accepted_by_user" | "declined_by_user";
  createdAt: string;
  price?: number;
  estimatedDeliveryTime?: string; // ISO Date string
}
// --- END TYPE DEFINITIONS ---

interface DetailedResponseCardProps {
  response: ApiResponse;
  request: ApiRequest; // Full request object to access details like offeringTime, status, acceptedResponseId
}

const DetailedResponseCard: React.FC<DetailedResponseCardProps> = ({
  response,
  request,
}) => {
  const cartContext = useContext(CartContext);
  if (!cartContext) {
    // This case should ideally be handled by ensuring CartProvider wraps this component tree.
    // For robustness, you might throw an error or return a fallback UI.
    // console.error("DetailedResponseCard must be used within a CartProvider");
    // To satisfy TypeScript for now if CartContext is undefined:
    return <div>Error: Cart context not available.</div>;
  }
  const { createCartItem, loading: cartLoading } = cartContext;
  const [acceptLoading, setAcceptLoading] = useState(false);

  const isAcceptedResponse =
    typeof request.acceptedResponseId === "string"
      ? request.acceptedResponseId === response._id
      : request.acceptedResponseId?._id === response._id;

  const canAddToCart =
    isAcceptedResponse && request.status === "confirmed" && response.dish;

  const handleAddToCart = async () => {
    if (!response.dish || !request.dishRequest) return;

    let dineInTime: Date | null = null;
    let pickupTime: Date | null = null;

    if (request.dishRequest.offeringTime) {
      if (request.dishRequest.offering === "Dine In") {
        dineInTime = new Date(request.dishRequest.offeringTime);
      } else if (request.dishRequest.offering === "Pickup") {
        pickupTime = new Date(request.dishRequest.offeringTime);
      }
    }

    try {
      await createCartItem(
        response.dish._id,
        response.dish.name,
        response.price || response.dish.price, // Use response price if available, else dish price
        request.dishRequest.quantity || 1, // Use request quantity or default to 1
        response.dish.photos?.[0] || "", // First photo or empty string
        request.dishRequest.offering,
        response.host._id,
        dineInTime,
        pickupTime
      );
      toast.success(`${response.dish.name} added to cart!`);
    } catch (error) {
      console.error("Failed to add to cart:", error);
      toast.error("Failed to add item to cart. Please try again.");
    }
  };

  const handleAcceptResponse = async () => {
    if (!response || !request) return;
    setAcceptLoading(true);
    console.log(
      `Attempting to accept response: ${response._id} for request: ${request._id}`
    );

    // Simulate API Call
    try {
      await acceptResponse(request._id, response._id);
      toast.success(`Response from ${response.host.name} accepted!`);
      // Optionally, trigger a re-fetch or update of parent component's state
      // to reflect the change in request status and acceptedResponseId.
      // For now, the button will disappear if the parent component re-renders
      // with an updated request object where status is no longer 'pending_responses'.
    } catch (error) {
      console.error("Failed to accept response:", error);
      toast.error("Failed to accept response. Please try again.");
    } finally {
      setAcceptLoading(false);
    }
  };

  return (
    <Card className="mb-4 w-full">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div className="flex items-center">
            <Avatar className="h-10 w-10 mr-3">
              <AvatarImage src={response.host?.avatar} />
              <AvatarFallback>{response.host?.name?.charAt(0)}</AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-xl">{response.host?.name}</CardTitle>
              <CardDescription>
                Responded on: {format(new Date(response.createdAt), "PPP p")}
              </CardDescription>
            </div>
          </div>
          <Badge
            variant={
              response.status === "accepted_by_user" ? "default" : "outline" // Changed "success" to "default"
            }
            className={
              response.status === "accepted_by_user"
                ? "bg-green-500 text-white"
                : ""
            } // Optional: Add class for specific styling
          >
            {response.status.replace(/_/g, " ")}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-gray-700">{response.message}</p>
        {response.price && (
          <p className="font-semibold text-lg">
            Price: ${response.price.toFixed(2)}
          </p>
        )}

        {response.dish && (
          <div className="mt-4 border-t pt-4">
            <h4 className="font-semibold text-lg mb-2">
              Dish Offered: {response.dish.name}
            </h4>
            {response.dish.photos && response.dish.photos.length > 0 ? (
              <Carousel className="w-full max-w-xs mx-auto mb-4">
                <CarouselContent>
                  {response.dish.photos.map(
                    (
                      photo: string,
                      index: number // Added types for photo and index
                    ) => (
                      <CarouselItem key={index}>
                        <div className="p-1">
                          <Card>
                            <CardContent className="flex aspect-square items-center justify-center p-0 overflow-hidden">
                              <Image
                                src={photo}
                                alt={`${response.dish?.name} image ${
                                  index + 1
                                }`}
                                width={300}
                                height={300}
                                className="object-cover w-full h-full"
                              />
                            </CardContent>
                          </Card>
                        </div>
                      </CarouselItem>
                    )
                  )}
                </CarouselContent>
                <CarouselPrevious />
                <CarouselNext />
              </Carousel>
            ) : (
              <p className="text-sm text-gray-500">
                No photos available for this dish.
              </p>
            )}
            {response.dish.description && (
              <p className="text-sm text-gray-600">
                <span className="font-medium">Description:</span>{" "}
                {response.dish.description}
              </p>
            )}
          </div>
        )}
      </CardContent>
      {canAddToCart && (
        <CardFooter>
          <Button
            onClick={handleAddToCart}
            disabled={cartLoading}
            className="w-full"
          >
            <ShoppingCart className="mr-2 h-4 w-4" />
            {cartLoading ? "Adding to Cart..." : "Add to Cart"}
          </Button>
        </CardFooter>
      )}
      {isAcceptedResponse && request.status !== "confirmed" && (
        <CardFooter className="bg-blue-50 border-t border-blue-200">
          <p className="text-blue-700 font-medium text-sm">
            This response is accepted. Waiting for request confirmation to add
            to cart.
          </p>
        </CardFooter>
      )}
      {response.status === "accepted_by_user" &&
        !isAcceptedResponse &&
        request.acceptedResponseId && (
          <CardFooter className="bg-yellow-50 border-t border-yellow-200">
            <p className="text-yellow-700 font-medium text-sm">
              This response was previously accepted but another response is now
              the active accepted one.
            </p>
          </CardFooter>
        )}

      {/* Section for Accept Response Button */}
      {request.status === "pending_responses" &&
        response.status === "pending_user_action" && (
          <CardFooter className="border-t pt-4">
            <Button
              onClick={handleAcceptResponse}
              disabled={acceptLoading}
              className="w-full bg-green-600 hover:bg-green-700 text-white"
            >
              {acceptLoading ? "Accepting..." : "Accept Response"}
            </Button>
          </CardFooter>
        )}
    </Card>
  );
};

export default DetailedResponseCard;
