import axiosInstance, { endpoints } from "@/utils/axiosInstance";

// Types for requests
interface RequestData {
  title: string;
  description?: string;
  ingredients: string[];
  offering: string;
  minServing: number;
  maxServing: number;
  quantity: number;
  offeringTime?: Date;

  location: {
    street: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
    latitude?: number;
    longitude?: number;
  };
}

interface ResponseData {
  message?: string;
  dishId?: string;
}

// Create a new request
export const createRequest = async (requestData: RequestData) => {
  try {
    const response = await axiosInstance.post(
      endpoints.requests.create,
      requestData
    );
    return response.data;
  } catch (error) {
    console.error("Error creating request:", error);
    throw error;
  }
};

// Get request feed (public requests)
export const getRequestFeed = async (params?: {
  page?: number;
  limit?: number;
}) => {
  try {
    const response = await axiosInstance.get(endpoints.requests.feed, {
      params,
    });
    return response.data;
  } catch (error) {
    console.error("Error getting request feed:", error);
    throw error;
  }
};

// Get user's requests
export const getUserRequests = async (params?: {
  status?: string;
  page?: number;
  limit?: number;
}) => {
  try {
    const response = await axiosInstance.get(endpoints.requests.user, {
      params,
    });
    return response.data;
  } catch (error) {
    console.error("Error getting user requests:", error);
    throw error;
  }
};

// Get host responses for user's requests
export const getHostResponses = async () => {
  try {
    const response = await axiosInstance.get(endpoints.requests.hostResponses);
    return response.data;
  } catch (error) {
    console.error("Error getting host responses:", error);
    throw error;
  }
};

// Respond to a request (for hosts)
export const respondToRequest = async (
  requestId: string,
  responseData: ResponseData
) => {
  try {
    const response = await axiosInstance.post(
      `${endpoints.requests.respond(requestId)}`,
      responseData
    );
    return response.data;
  } catch (error) {
    console.error("Error responding to request:", error);
    throw error;
  }
};

// Accept a response to a request
export const acceptResponse = async (requestId: string, responseId: string) => {
  try {
    const response = await axiosInstance.post(
      `${endpoints.requests.acceptResponse(requestId, responseId)}`
    );
    return response.data;
  } catch (error) {
    console.error("Error accepting response:", error);
    throw error;
  }
};

// Get a single request by ID
export const getRequestById = async (requestId: string) => {
  try {
    const response = await axiosInstance.get(
      `${endpoints.requests.getById(requestId)}`
    );
    return response.data;
  } catch (error) {
    console.error("Error getting request details:", error);
    throw error;
  }
};

// Get responses for a specific request
export const getResponsesForRequest = async (
  requestId: string,
  params?: { page?: number; limit?: number }
) => {
  try {
    const response = await axiosInstance.get(
      `${endpoints.requests.responses(requestId)}`, // Assuming you have an endpoint like this
      { params }
    );
    return response.data;
  } catch (error) {
    console.error("Error getting responses for request:", error);
    throw error;
  }
};

// Update a request
export const updateRequest = async (
  requestId: string,
  requestData: Partial<RequestData>
) => {
  try {
    const response = await axiosInstance.put(
      `${endpoints.requests.update(requestId)}`,
      requestData
    );
    return response.data;
  } catch (error) {
    console.error("Error updating request:", error);
    throw error;
  }
};

// Delete a request
export const deleteRequest = async (requestId: string) => {
  try {
    const response = await axiosInstance.delete(
      `${endpoints.requests.delete(requestId)}`
    );
    return response.data;
  } catch (error) {
    console.error("Error deleting request:", error);
    throw error;
  }
};
